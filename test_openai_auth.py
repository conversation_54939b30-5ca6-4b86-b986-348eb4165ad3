#!/usr/bin/env python3
"""
Test script to verify Azure OpenAI authentication
"""
import os
import asyncio
from openai import AsyncAzureOpenAI

async def test_openai_auth():
    # Get environment variables
    api_key = os.environ.get("AZURE_OPENAI_API_KEY")
    endpoint = os.environ.get("AZURE_OPENAI_ENDPOINT")
    deployment = os.environ.get("AZURE_OPENAI_DEPLOYMENT")
    
    print(f"API Key present: {'Yes' if api_key else 'No'}")
    print(f"Endpoint: {endpoint}")
    print(f"Deployment: {deployment}")
    
    if not api_key:
        print("❌ No API key found. Set AZURE_OPENAI_API_KEY environment variable.")
        return
    
    if not endpoint:
        print("❌ No endpoint found. Set AZURE_OPENAI_ENDPOINT environment variable.")
        return
        
    if not deployment:
        print("❌ No deployment found. Set AZURE_OPENAI_DEPLOYMENT environment variable.")
        return
    
    # Test API key authentication
    try:
        client = AsyncAzureOpenAI(
            api_key=api_key,
            api_version="2024-08-01-preview",
            azure_endpoint=endpoint,
        )
        
        print("🔄 Testing API key authentication...")
        
        response = await client.chat.completions.create(
            model=deployment,
            messages=[
                {"role": "user", "content": "Hello, this is a test message."}
            ],
            max_tokens=10
        )
        
        print("✅ API key authentication successful!")
        print(f"Response: {response.choices[0].message.content}")
        
    except Exception as e:
        print(f"❌ API key authentication failed: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        
        # Check if it's a specific error
        if "401" in str(e):
            print("💡 This suggests the API key is invalid or expired.")
        elif "403" in str(e) or "Forbidden" in str(e):
            print("💡 This suggests the API key doesn't have permission to access this deployment.")
        elif "404" in str(e):
            print("💡 This suggests the endpoint or deployment name is incorrect.")
    
    finally:
        await client.close()

if __name__ == "__main__":
    asyncio.run(test_openai_auth())
