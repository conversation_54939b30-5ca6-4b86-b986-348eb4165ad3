{"locale": "ff-Adlm", "long": {"year": {"previous": "𞤪𞤮𞤱𞤢𞤲𞤭", "current": "𞤸𞤭𞤳𞥆𞤢", "next": "𞤸𞤭𞤼𞤢𞥄𞤲𞤣𞤫 𞤢𞤪𞤮𞥅𞤪𞤫", "past": {"one": "𞤱𞤢𞤯𞤭𞥅 𞤸𞤭𞤼𞤢𞥄𞤲𞤣𞤫 {0}", "other": "{0} 𞤳𞤭𞤼𞤢𞥄𞤯𞤫 𞤪𞤫𞤱𞤢𞤲𞤭"}, "future": {"one": "𞤲𞤣𞤫𞤪 {0} 𞤸𞤭𞤼𞤢𞥄𞤲𞤣𞤫", "other": "𞤲𞤣𞤫𞤪 {0} 𞤳𞤭𞤼𞤢𞥄𞤤𞤫"}}, "quarter": {"previous": "𞤲𞤢𞤴𞤩𞤭𞥅𞤪𞤫 𞤬𞤫𞤰𞥆𞤵𞤲𞥋𞤣𞤫", "current": "𞤲𞤣𞤫𞥅 𞤲𞤢𞤴𞤩𞤭𞥅𞤪𞤫", "next": "𞤲𞤢𞤴𞤩𞤭𞥅𞤪𞤫 𞤢𞤪𞤮𞥅𞤪𞤫", "past": {"one": "𞤱𞤢𞤯𞤭𞥅 𞤲𞤢𞤴𞤩𞤭𞥅𞤪𞤫 {0}", "other": "{0} 𞤲𞤢𞤴𞤢𞤩𞤭𞥅𞤶𞤫 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": {"one": "𞤲𞤣𞤫𞤪 𞤲𞤢𞤴𞤩𞤭𞥅𞤪𞤫 {0}", "other": "𞤲𞤣𞤫𞤪 𞤲𞤢𞤴𞤩𞤭𞥅𞤶𞤫 {0}"}}, "month": {"previous": "𞤤𞤫𞤱𞤪𞤵 𞤬𞤫𞤰𞥆𞤵𞤲𞥋𞤣𞤵", "current": "𞤲𞥋𞤣𞤵𞥅 𞤯𞤮𞤮 𞤤𞤫𞤱𞤪𞤵", "next": "𞤤𞤫𞤱𞤪𞤵 𞤢𞤪𞤢𞤴𞤲𞥋𞤣𞤵", "past": {"one": "{0} 𞤤𞤫𞤱𞤪𞤵 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤵", "other": "{0} 𞤤𞤫𞤦𞥆𞤭 𞤱𞤵𞤤𞤭𞥅𞤯𞤭"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤤𞤫𞤱𞤪𞤵", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤤𞤫𞤦𞥆𞤭"}}, "week": {"previous": "𞤴𞤮𞤲𞤼𞤫𞤪𞤫 𞤬𞤫𞤰𞥆𞤵𞤲𞥋𞤣𞤫", "current": "𞤲𞤣𞤫𞥅 𞤯𞤮𞤮 𞤴𞤮𞤲𞤼𞤫𞤪𞤫", "next": "𞤴𞤮𞤲𞤼𞤫𞤪𞤫 𞤢𞤪𞤢𞤴𞤲𞤣𞤫", "past": {"one": "{0} 𞤴𞤮𞤲𞤼𞤫𞤪𞤫 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤶𞤮𞤲𞤼𞤫 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤴𞤮𞤲𞤼𞤫𞤪𞤫", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤶𞤮𞤲𞤼𞤫"}}, "day": {"previous": "𞤸𞤢𞤲𞤳𞤭", "current": "𞤸𞤢𞤲𞤣𞤫", "next": "𞤶𞤢𞤲𞤺𞤮", "past": {"one": "{0} 𞤻𞤢𞤤𞥆𞤢𞤤 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤻𞤢𞤤𞥆𞤫 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤻𞤢𞤤𞥆𞤢𞤤", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤻𞤢𞤤𞥆𞤫"}}, "hour": {"current": "𞤲𞥋𞤣𞤭𞥅 𞤯𞤮𞤮 𞤲𞥋𞤶𞤢𞤥𞤲𞥋𞤣𞤭", "past": {"one": "{0} 𞤲𞥋𞤶𞤢𞤥𞤲𞥋𞤣𞤭 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤭", "other": "{0} 𞤲𞥋𞤶𞤢𞤥𞤤𞤭 𞤱𞤵𞤤𞤭𞥅𞤯𞤭"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤲𞥋𞤶𞤢𞤥𞤲𞥋𞤣𞤭", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤲𞥋𞤶𞤢𞤥𞤤𞤭"}}, "minute": {"current": "𞤲𞥋𞤣𞤫𞥅 𞤯𞤮𞤮 𞤸𞤮𞤶𞤮𞤥𞤪𞤫", "past": {"one": "{0} 𞤸𞤮𞤶𞤮𞤥𞤪𞤫 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤳𞤮𞤶𞤮𞤥𞤶𞤫 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤸𞤮𞤶𞤮𞤥𞤪𞤫", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤳𞤮𞤶𞤮𞤥𞤶𞤫"}}, "second": {"current": "𞤶𞤮𞥅𞤲𞤭", "past": {"one": "{0} 𞤳𞤭𞤲𞤰𞤢𞤤 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤺𞤢𞤤", "other": "{0} 𞤳𞤭𞤲𞤰𞤫 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤳𞤭𞤲𞤰𞤢𞤤", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤳𞤭𞤲𞤰𞤫"}}}, "short": {"year": {"previous": "𞤪𞤮𞤱𞤢𞤲𞤭", "current": "𞤸𞤭𞤳𞥆𞤢", "next": "𞤸𞤭𞤼𞤢𞥄𞤲𞤣𞤫 𞤢𞤪𞤮𞥅𞤪𞤫", "past": {"one": "{0} 𞤸𞤭𞤼. 𞤪𞤫𞤱𞤢𞤲𞤭", "other": "{0} 𞤳𞤭𞤼. 𞤪𞤫𞤱𞤢𞤲𞤭"}, "future": {"one": "𞤲𞤣𞤫𞤪 𞤸𞤭𞤼. {0}", "other": "𞤲𞤣𞤫𞤪 𞤳𞤭𞤼. {0}"}}, "quarter": {"previous": "𞤲𞤢𞤴. 𞤬𞤫𞤰.", "current": "𞤲𞤣𞤫𞥅 𞤲𞤢𞤴.", "next": "𞤲𞤢𞤴. 𞤢𞤪𞤮𞥅𞤪𞤫", "past": {"one": "𞤱𞤢𞤯𞤭𞥅 𞤲𞤢𞤴. {0}", "other": "{0} 𞤲𞤢𞤴𞤶. 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": "𞤲𞤣𞤫𞤪 𞤲𞤢𞤴. {0}"}, "month": {"previous": "𞤤𞤫𞤱. 𞤬𞤫𞤰𞥆𞤵𞤲𞥋𞤣𞤵", "current": "𞤲𞤣𞤮𞥅 𞤯𞤮𞤮 𞤤𞤫𞤱.", "next": "𞤤𞤫𞤱. 𞤸𞤭𞤳𞥆𞤭𞥅𞤲𞤣𞤵", "past": {"one": "{0} 𞤤𞤫𞤱. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤵", "other": "{0} 𞤤𞤫𞤦. 𞤱𞤵𞤤𞤭𞥅𞤯𞤭"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤤𞤫𞤱.", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤤𞤫𞤦."}}, "week": {"previous": "𞤴𞤼. 𞤬𞤫𞤰𞥆𞤵𞤲𞥋𞤣𞤫", "current": "𞤲𞤣𞤫𞥅 𞤯𞤮𞤮 𞤴𞤼.", "next": "𞤴𞤼. 𞤸𞤭𞤳𞥆𞤭𞥅𞤲𞤣𞤫", "past": {"one": "{0} 𞤴𞤼. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤶𞤼. 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤴𞤼.", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤶𞤼."}}, "day": {"previous": "𞤸𞤢𞤲𞤳𞤭", "current": "𞤸𞤢𞤲𞤣𞤫", "next": "𞤶𞤢𞤲𞤺𞤮", "past": {"one": "{0} 𞤻𞤢𞤤𞥆𞤢𞤤 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤻𞤢𞤤𞥆𞤫 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤻𞤢𞤤𞥆𞤢𞤤", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤻𞤢𞤤𞥆𞤫"}}, "hour": {"current": "𞤐𞤣𞤭𞥅 𞤲𞤶𞤢𞤥𞤲𞤣𞤭", "past": {"one": "{0} 𞤶𞤢. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤭", "other": "{0} 𞤶𞤢. 𞤱𞤵𞤤𞤭𞥅𞤯𞤭"}, "future": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤶𞤢."}, "minute": {"current": "𞤲𞤣𞤫𞥅 𞤸𞤮𞤶𞤮𞤥𞤢𞥄𞤪𞤫", "past": {"one": "{0} 𞤸𞤮𞤶. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤳𞤮𞤶. 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤸𞤮𞤶."}, "second": {"current": "𞤶𞤮𞥅𞤲𞤭", "past": {"one": "{0} 𞤳𞤭𞤲. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤺𞤢𞤤", "other": "{0} 𞤳𞤭𞤲. 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤳𞤭𞤲."}}, "narrow": {"year": {"previous": "𞤪𞤮𞤱𞤢𞤲𞤭", "current": "𞤸𞤭𞤳𞥆𞤢", "next": "𞤸𞤭𞤼 𞤢𞤪𞤮𞥅𞤪𞤫", "past": {"one": "{0} 𞤸𞤭𞤼. 𞤪𞤫𞤱𞤢𞤲𞤭", "other": "{0} 𞤳𞤭𞤼. 𞤪𞤫𞤱𞤢𞤲𞤭"}, "future": {"one": "𞤲𞤣𞤫𞤪 𞤸𞤭𞤼. {0}", "other": "𞤲𞤣𞤫𞤪 𞤳𞤭𞤼. {0}"}}, "quarter": {"previous": "𞤲𞤢𞤴. 𞤬𞤫𞤰.", "current": "𞤲𞤣𞤫𞥅 𞤲𞤢𞤴.", "next": "𞤲𞤢𞤴. 𞤢𞤪𞤮𞥅𞤪𞤫", "past": {"one": "{0} 𞤲𞤢𞤴. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤲𞤢𞤴𞤶. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫"}, "future": "𞤲𞤣𞤫𞤪 𞤲𞤢𞤴. {0}"}, "month": {"previous": "𞤤𞤫𞤱. 𞤬𞤫𞤰𞥆𞤵𞤲𞥋𞤣𞤵", "current": "𞤲𞤣𞤮𞥅 𞤯𞤮𞤮 𞤤𞤫𞤱.", "next": "𞤤𞤫𞤱. 𞤸𞤭𞤳𞥆𞤭𞥅𞤲𞤣𞤵", "past": {"one": "{0} 𞤤𞤫𞤱. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤵", "other": "{0} 𞤤𞤫𞤦. 𞤱𞤵𞤤𞤭𞥅𞤯𞤭"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤤𞤫𞤱.", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤤𞤫𞤦."}}, "week": {"previous": "𞤴𞤼. 𞤬𞤫𞤰𞥆𞤵𞤲𞥋𞤣𞤫", "current": "𞤲𞤣𞤫𞥅 𞤯𞤮𞤮 𞤴𞤼.", "next": "𞤴𞤼. 𞤸𞤭𞤳𞥆𞤭𞥅𞤲𞤣𞤫", "past": {"one": "{0} 𞤴𞤼. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤶𞤼. 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤴𞤼.", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤶𞤼."}}, "day": {"previous": "𞤸𞤢𞤲𞤳𞤭", "current": "𞤸𞤢𞤲𞤣𞤫", "next": "𞤶𞤢𞤲𞤺𞤮", "past": {"one": "{0} 𞤻𞤢𞤤𞥆𞤢𞤤 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤻𞤢𞤤𞥆𞤫 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": {"one": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤻𞤢𞤤𞥆𞤢𞤤", "other": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤻𞤢𞤤𞥆𞤫"}}, "hour": {"current": "𞤐𞤣𞤭𞥅 𞤲𞤶𞤢𞤥𞤲𞤣𞤭", "past": {"one": "{0} 𞤶𞤢. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤭", "other": "{0} 𞤶𞤢. 𞤱𞤵𞤤𞤭𞥅𞤯𞤭"}, "future": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤶𞤢."}, "minute": {"current": "𞤐𞤣𞤫𞥅 𞤸𞤮𞤶𞤮𞤥𞤢𞥄𞤪𞤫", "past": {"one": "{0} 𞤸𞤮𞤶. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤣𞤫", "other": "{0} 𞤸𞤮𞤶. 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤸𞤮𞤶."}, "second": {"current": "𞤶𞤮𞥅𞤲𞤭", "past": {"one": "{0} 𞤳𞤭𞤲. 𞤱𞤵𞤤𞤭𞥅𞤲𞥋𞤺𞤢𞤤", "other": "{0} 𞤳𞤭𞤲. 𞤱𞤵𞤤𞤭𞥅𞤯𞤫"}, "future": "𞤲𞥋𞤣𞤫𞤪 {0} 𞤳𞤭𞤲."}}}