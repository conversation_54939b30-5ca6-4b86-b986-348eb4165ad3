{"locale": "ccp", "long": {"year": {"previous": "𑄉𑄬𑄣𑄳𑄠𑄬 𑄝𑄧𑄏𑄧𑄢𑄴", "current": "𑄃𑄬 𑄝𑄧𑄏𑄧𑄢𑄴", "next": "𑄎𑄬𑄢𑄧 𑄝𑄧𑄏𑄧𑄢𑄴", "past": "{0} 𑄝𑄧𑄏𑄧𑄢𑄴 𑄃𑄉𑄬", "future": "{0} 𑄝𑄧𑄏𑄧𑄢𑄬"}, "quarter": {"previous": "𑄉𑄬𑄣𑄳𑄠𑄬 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴", "current": "𑄃𑄳𑄆𑄬 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴", "next": "𑄛𑄧𑄢𑄬 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴", "past": "{0} 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴 𑄃𑄉𑄬", "future": {"one": "{0} 𑄖𑄨𑄚𑄴𑄟𑄏𑄬", "other": "{0} 𑄖𑄨𑄚𑄟𑄏𑄬"}}, "month": {"previous": "𑄉𑄬𑄣𑄧𑄘𑄬 𑄟𑄏𑄴", "current": "𑄃𑄳𑄆𑄬 𑄟𑄏𑄴", "next": "𑄛𑄧𑄢𑄬 𑄟𑄏𑄴", "past": "{0} 𑄟𑄏𑄧 𑄃𑄉𑄬", "future": "{0} 𑄟𑄏𑄬"}, "week": {"previous": "𑄉𑄬𑄣𑄧𑄘𑄬 𑄥𑄛𑄴𑄖", "current": "𑄃𑄳𑄆𑄬 𑄥𑄛𑄴𑄖", "next": "𑄛𑄧𑄢𑄬 𑄥𑄛𑄴𑄖", "past": "{0} 𑄥𑄛𑄴𑄖 𑄃𑄉𑄬", "future": "{0} 𑄥𑄛𑄴𑄖𑄠𑄴"}, "day": {"previous": "𑄉𑄬𑄣𑄴𑄣𑄳𑄠𑄇𑄬𑄣𑄳𑄠𑄬", "previous-2": "𑄉𑄬𑄣𑄧𑄘𑄬 𑄛𑄧𑄢𑄴𑄥𑄪", "current": "𑄃𑄬𑄌𑄴𑄥𑄳𑄠", "next": "𑄃𑄬𑄎𑄬𑄖𑄴𑄖𑄳𑄠𑄇𑄬𑄣𑄳𑄠𑄬", "next-2": "𑄃𑄬𑄎𑄬𑄖𑄴𑄖𑄳𑄠𑄬 𑄛𑄧𑄢𑄴𑄥𑄪", "past": "{0} 𑄘𑄨𑄚𑄴 𑄃𑄉𑄬", "future": "{0} 𑄘𑄨𑄚𑄮 𑄟𑄧𑄖𑄴𑄙𑄳𑄠"}, "hour": {"current": "𑄃𑄳𑄆𑄬 𑄊𑄮𑄚𑄴𑄓𑄠𑄴", "past": "{0} 𑄊𑄮𑄚𑄴𑄓 𑄃𑄉𑄬", "future": "{0} 𑄊𑄮𑄚𑄴𑄓𑄠𑄴"}, "minute": {"current": "𑄃𑄳𑄆𑄬 𑄟𑄨𑄚𑄨𑄖𑄴", "past": "{0} 𑄟𑄨𑄚𑄨𑄖𑄴 𑄃𑄉𑄬", "future": "{0} 𑄟𑄨𑄚𑄨𑄘𑄬"}, "second": {"current": "𑄃𑄨𑄇𑄴𑄅𑄚𑄪", "past": "{0} 𑄥𑄬𑄉𑄬𑄚𑄴 𑄃𑄉𑄬", "future": "{0} 𑄥𑄬𑄉𑄬𑄚𑄴𑄘𑄬"}}, "short": {"year": {"previous": "𑄉𑄬𑄣𑄳𑄠𑄬 𑄝𑄧𑄏𑄧𑄢𑄴", "current": "𑄃𑄳𑄆𑄬 𑄝𑄧𑄏𑄧𑄢𑄴", "next": "𑄛𑄧𑄢𑄬 𑄝𑄧𑄏𑄧𑄢𑄴", "past": "{0} 𑄝𑄧𑄏𑄧𑄢𑄴 𑄃𑄉𑄬", "future": "{0} 𑄝𑄧𑄏𑄧𑄢𑄬"}, "quarter": {"previous": "𑄉𑄬𑄣𑄳𑄠𑄬 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴", "current": "𑄃𑄳𑄆𑄬 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴", "next": "𑄛𑄧𑄢𑄬 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴", "past": {"one": "{0} 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴 𑄃𑄉𑄬", "other": "{0}𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴 𑄃𑄉𑄬"}, "future": "{0} 𑄖𑄨𑄚𑄴𑄟𑄏𑄬"}, "month": {"previous": "𑄉𑄬𑄣𑄧𑄉𑄬 𑄟𑄏𑄴", "current": "𑄃𑄳𑄆𑄬 𑄟𑄏𑄴", "next": "𑄛𑄧𑄢𑄬 𑄟𑄏𑄴", "past": {"one": "{0} 𑄇𑄏𑄧 𑄃𑄉𑄬", "other": "{0} 𑄟𑄏𑄧 𑄃𑄉𑄬"}, "future": "{0} 𑄟𑄏𑄬"}, "week": {"previous": "𑄉𑄬𑄣𑄧𑄘𑄬 𑄥𑄛𑄴𑄖", "current": "𑄃𑄳𑄆𑄬 𑄥𑄛𑄴𑄖", "next": "𑄛𑄧𑄢𑄬 𑄥𑄛𑄴𑄖", "past": "{0} 𑄥𑄛𑄴𑄖 𑄃𑄉𑄬", "future": "{0} 𑄥𑄛𑄴𑄖𑄠𑄴"}, "day": {"previous": "𑄉𑄬𑄣𑄴𑄣𑄳𑄠𑄇𑄬𑄣𑄴𑄣𑄳𑄠𑄬", "previous-2": "𑄉𑄬𑄣𑄧𑄘𑄬 𑄛𑄧𑄢𑄴𑄥𑄪", "current": "𑄃𑄬𑄌𑄴𑄥𑄳𑄠𑄬", "next": "𑄃𑄬𑄎𑄬𑄖𑄴𑄖𑄳𑄠𑄇𑄬𑄣𑄴𑄣𑄳𑄠𑄬", "next-2": "𑄃𑄬𑄎𑄬𑄖𑄴𑄖𑄳𑄠𑄇𑄬𑄣𑄴𑄣𑄳𑄠𑄬 𑄛𑄧𑄢𑄴𑄥𑄪", "past": "{0} 𑄘𑄨𑄚𑄴 𑄃𑄉𑄬", "future": "{0} 𑄘𑄨𑄚𑄮 𑄟𑄧𑄖𑄴𑄙𑄳𑄠"}, "hour": {"current": "𑄃𑄳𑄆𑄬 𑄊𑄮𑄚𑄴𑄓𑄠𑄴", "past": "{0} 𑄊𑄮𑄚𑄴𑄓 𑄃𑄉𑄬", "future": "{0} 𑄊𑄮𑄚𑄴𑄓𑄠𑄴"}, "minute": {"current": "𑄃𑄳𑄆𑄬 𑄟𑄨𑄚𑄨𑄖𑄴", "past": "{0} 𑄟𑄨𑄚𑄨𑄖𑄴 𑄃𑄉𑄬", "future": "{0} 𑄟𑄨𑄚𑄨𑄘𑄬"}, "second": {"current": "𑄃𑄨𑄇𑄴𑄅𑄚𑄪", "past": "{0} 𑄥𑄬𑄉𑄬𑄚𑄴 𑄃𑄉𑄬", "future": "{0} 𑄥𑄬𑄉𑄬𑄚𑄴𑄘𑄬"}}, "narrow": {"year": {"previous": "𑄉𑄬𑄣𑄳𑄠𑄬 𑄝𑄧𑄏𑄧𑄢𑄴", "current": "𑄃𑄳𑄆𑄬 𑄝𑄧𑄏𑄧𑄢𑄴", "next": "𑄛𑄧𑄢𑄬 𑄝𑄧𑄏𑄧𑄢𑄴", "past": "{0} 𑄝𑄧𑄏𑄧𑄢𑄴 𑄃𑄉𑄬", "future": "{0} 𑄝𑄧𑄏𑄧𑄢𑄬"}, "quarter": {"previous": "𑄉𑄬𑄣𑄳𑄠𑄬 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴", "current": "𑄃𑄳𑄆𑄬 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴", "next": "𑄛𑄧𑄢𑄬 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴", "past": {"one": "{0} 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴 𑄃𑄉𑄬", "other": "{0} 𑄖𑄨𑄚𑄴𑄟𑄏𑄧𑄢𑄴 𑄃𑄬𑄉"}, "future": "{0} 𑄖𑄨𑄚𑄴𑄟𑄏𑄬"}, "month": {"previous": "𑄉𑄬𑄣𑄧𑄘𑄬 𑄟𑄏𑄴", "current": "𑄃𑄳𑄆𑄬 𑄟𑄏𑄴", "next": "𑄛𑄧𑄢𑄬 𑄟𑄏𑄴", "past": "{0} 𑄟𑄏𑄧 𑄃𑄉𑄬", "future": "{0} 𑄟𑄏𑄬"}, "week": {"previous": "𑄉𑄬𑄣𑄧𑄘𑄬 𑄥𑄛𑄴𑄖", "current": "𑄃𑄳𑄆𑄬 𑄥𑄛𑄴𑄖", "next": "𑄛𑄧𑄢𑄬 𑄥𑄛𑄴𑄖", "past": "{0} 𑄥𑄛𑄴𑄖𑄢𑄴 𑄃𑄉𑄬", "future": "{0} 𑄥𑄛𑄴𑄖𑄠𑄴"}, "day": {"previous": "𑄉𑄬𑄣𑄴𑄣𑄳𑄠𑄇𑄬𑄣𑄴𑄣𑄳𑄠𑄬", "previous-2": "𑄉𑄬𑄣𑄧𑄘𑄬 𑄛𑄧𑄢𑄴𑄥𑄪", "current": "𑄃𑄬𑄌𑄴𑄥𑄳𑄠𑄬", "next": "𑄃𑄬𑄎𑄬𑄖𑄴𑄖𑄳𑄠𑄇𑄬𑄣𑄴𑄣𑄳𑄠𑄬", "next-2": "𑄃𑄬𑄎𑄬𑄖𑄴𑄖𑄳𑄠𑄇𑄬𑄣𑄴𑄣𑄳𑄠𑄬 𑄛𑄧𑄢𑄴𑄥𑄪", "past": "{0} 𑄘𑄨𑄚𑄴 𑄃𑄉𑄬", "future": "{0} 𑄘𑄨𑄚𑄮 𑄟𑄧𑄖𑄴𑄙𑄳𑄠"}, "hour": {"current": "𑄃𑄳𑄆𑄬 𑄊𑄮𑄚𑄴𑄓𑄠𑄴", "past": "{0} 𑄊𑄮𑄚𑄴𑄓 𑄃𑄉𑄬", "future": "{0} 𑄊𑄮𑄚𑄴𑄓𑄠𑄴"}, "minute": {"current": "𑄃𑄳𑄆𑄬 𑄟𑄨𑄚𑄨𑄖𑄴", "past": "{0} 𑄟𑄨𑄚𑄨𑄖𑄴 𑄃𑄉𑄬", "future": "{0} 𑄟𑄨𑄚𑄨𑄘𑄬"}, "second": {"current": "𑄃𑄨𑄇𑄴𑄅𑄚𑄪", "past": "{0} 𑄥𑄬𑄉𑄬𑄚𑄴 𑄃𑄉𑄬", "future": {"one": "{0} 𑄥𑄬𑄉𑄬𑄚𑄴", "other": "{0} 𑄥𑄬𑄉𑄬𑄚𑄴𑄘𑄬"}}}}