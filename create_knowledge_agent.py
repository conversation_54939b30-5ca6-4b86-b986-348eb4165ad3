#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create a Knowledge Agent in Azure AI Search
"""
import os
import asyncio
import json
from azure.identity.aio import DefaultAzureCredential
from azure.search.documents.indexes.aio import SearchIndexClient
from azure.search.documents.indexes.models import (
    KnowledgeAgent as AzureSearchKnowledgeAgent,
    KnowledgeAgentTargetIndex,
    KnowledgeAgentAzureOpenAIModel,
    AzureOpenAIVectorizerParameters,
)

async def create_knowledge_agent():
    # Get environment variables
    search_endpoint = os.environ.get("SEARCH_SERVICE_ENDPOINT")
    search_index_name = os.environ.get("SEARCH_INDEX_NAME")
    knowledge_agent_name = os.environ.get("KNOWLEDGE_AGENT_NAME")
    openai_endpoint = os.environ.get("AZURE_OPENAI_ENDPOINT")
    openai_deployment = os.environ.get("AZURE_OPENAI_DEPLOYMENT")
    openai_model = os.environ.get("AZURE_OPENAI_MODEL_NAME")
    
    print(f"Search Endpoint: {search_endpoint}")
    print(f"Search Index: {search_index_name}")
    print(f"Agent Name: {knowledge_agent_name}")
    print(f"OpenAI Endpoint: {openai_endpoint}")
    print(f"OpenAI Deployment: {openai_deployment}")
    print(f"OpenAI Model: {openai_model}")
    
    if not all([search_endpoint, search_index_name, knowledge_agent_name, openai_endpoint, openai_deployment, openai_model]):
        print("❌ Missing required environment variables")
        return
    
    try:
        # Create credentials
        credential = DefaultAzureCredential()
        
        # Create search index client
        index_client = SearchIndexClient(
            endpoint=search_endpoint,
            credential=credential,
        )
        
        print("🔄 Creating Knowledge Agent...")
        
        # Create the knowledge agent
        agent = AzureSearchKnowledgeAgent(
            name=knowledge_agent_name,
            target_indexes=[
                KnowledgeAgentTargetIndex(
                    index_name=search_index_name,
                    default_include_reference_source_data=True,
                )
            ],
            models=[
                KnowledgeAgentAzureOpenAIModel(
                    azure_open_ai_parameters=AzureOpenAIVectorizerParameters(
                        resource_url=openai_endpoint,
                        deployment_name=openai_deployment,
                        model_name=openai_model,
                    )
                )
            ],
        )
        
        result = await index_client.create_or_update_agent(agent)
        
        print("✅ Knowledge Agent created successfully!")
        print(f"Agent Name: {result.name}")
        print(f"Target Indexes: {[idx.index_name for idx in result.target_indexes]}")
        
    except Exception as e:
        print(f"❌ Failed to create Knowledge Agent: {str(e)}")
        print(f"Error type: {type(e).__name__}")
        
        if "403" in str(e) or "Forbidden" in str(e):
            print("💡 You need 'Search Service Contributor' or 'Search Index Data Contributor' role")
        elif "401" in str(e):
            print("💡 Authentication failed - check your Azure credentials")
        elif "404" in str(e):
            print("💡 Search service or index not found - check your configuration")
    
    finally:
        await credential.close()
        await index_client.close()

if __name__ == "__main__":
    asyncio.run(create_knowledge_agent())
