# PowerShell script to create Knowledge Agent via REST API
param(
    [string]$SearchServiceName = "adekatsowaisearch",
    [string]$ResourceGroup = "AI_Indira",
    [string]$AgentName = "my-knowledge-agent",
    [string]$IndexName = "sowfile_index",
    [string]$OpenAIEndpoint = "https://test-openai-test.openai.azure.com/",
    [string]$DeploymentName = "gpt-4.1",
    [string]$ModelName = "gpt-4.1"
)

Write-Host "Creating Knowledge Agent via REST API..." -ForegroundColor Yellow

# Get access token
try {
    $token = az account get-access-token --resource https://search.azure.com --query accessToken -o tsv
    if (-not $token) {
        throw "Failed to get access token"
    }
    Write-Host "✅ Got access token" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to get access token: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Prepare the request body
$body = @{
    name = $AgentName
    targetIndexes = @(
        @{
            indexName = $IndexName
            defaultIncludeReferenceSourceData = $true
        }
    )
    models = @(
        @{
            azureOpenAIParameters = @{
                resourceUrl = $OpenAIEndpoint
                deploymentName = $DeploymentName
                modelName = $ModelName
            }
        }
    )
} | ConvertTo-Json -Depth 10

Write-Host "Request body:" -ForegroundColor Cyan
Write-Host $body

# Prepare headers
$headers = @{
    'Authorization' = "Bearer $token"
    'Content-Type' = 'application/json'
    'api-version' = '2024-11-01-preview'
}

# Make the REST API call
$uri = "https://$SearchServiceName.search.windows.net/knowledgeAgents/$AgentName"

try {
    Write-Host "Making REST API call to: $uri" -ForegroundColor Yellow
    
    $response = Invoke-RestMethod -Uri $uri -Method PUT -Body $body -Headers $headers
    
    Write-Host "✅ Knowledge Agent created successfully!" -ForegroundColor Green
    Write-Host "Agent Name: $($response.name)" -ForegroundColor Green
    Write-Host "Target Indexes: $($response.targetIndexes | ForEach-Object { $_.indexName })" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Failed to create Knowledge Agent" -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.Response.StatusCode -eq 403) {
        Write-Host "💡 You need 'Search Service Contributor' or 'Search Index Data Contributor' role" -ForegroundColor Yellow
    } elseif ($_.Exception.Response.StatusCode -eq 401) {
        Write-Host "💡 Authentication failed - check your Azure credentials" -ForegroundColor Yellow
    } elseif ($_.Exception.Response.StatusCode -eq 404) {
        Write-Host "💡 Search service or index not found - check your configuration" -ForegroundColor Yellow
    }
    
    # Try to get more details from the response
    try {
        $errorDetails = $_.ErrorDetails.Message | ConvertFrom-Json
        Write-Host "Error Details: $($errorDetails.error.message)" -ForegroundColor Red
    } catch {
        # Ignore if we can't parse error details
    }
}
